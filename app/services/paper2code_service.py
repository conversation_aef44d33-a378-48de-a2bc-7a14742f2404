import os
import shutil
import tempfile
import zipfile
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any
from uuid import UUID
import asyncio

from fastapi import UploadFile, HTTPException
from tortoise.exceptions import DoesNotExist

from app.core.logging import get_logger
from app.models.paper2code import <PERSON>2<PERSON>odeJob, Paper2CodeFile, Paper2CodeLog, Paper2CodeStatus
from app.models.user import User
from app.api.schemas.role import InsetRole
from app.agents.paper2code.pdf_process import paper2json, process_pdf_json
from app.agents.paper2code.planning import run_planning
from app.agents.paper2code.analyzing import run_analyzing
from app.agents.paper2code.coding import run_coding
from app.agents.paper2code.extract_config import run_extract_config

logger = get_logger(__name__)


class Paper2CodeService:
    """Paper2Code服务类"""

    def __init__(self):
        self.base_storage_path = os.path.join("storage", "paper2code")
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.supported_extensions = ['.pdf', '.docx']
        self.supported_mime_types = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]

    async def create_job(
        self,
        file: UploadFile,
        job_name: str,
        description: Optional[str],
        current_user: User
    ) -> Paper2CodeJob:
        """创建新的Paper2Code任务"""

        # 验证文件
        await self._validate_file(file)

        # 创建存储目录
        job_id = str(uuid.uuid4())
        job_storage_path = os.path.join(self.base_storage_path, job_id)
        os.makedirs(job_storage_path, exist_ok=True)

        # 保存原始文件
        file_content = await file.read()
        file_extension = Path(file.filename).suffix.lower()
        original_file_path = os.path.join(job_storage_path, f"original{file_extension}")

        with open(original_file_path, "wb") as f:
            f.write(file_content)

        # 创建任务记录
        job = await Paper2CodeJob.create(
            job_name=job_name,
            description=description,
            original_file_name=file.filename,
            original_file_path=original_file_path,
            original_file_size=len(file_content),
            file_type=file_extension[1:],  # 去掉点号
            mime_type=file.content_type,
            output_directory=job_storage_path,
            status=Paper2CodeStatus.UPLOADED,
            created_by_id=current_user.id,
            organization_id=current_user.organization.id if current_user.organization else None
        )

        # 记录日志
        await self._log_job_event(job, "UPLOAD", "INFO", f"文件上传成功: {file.filename}")

        # 异步开始处理
        asyncio.create_task(self._process_job(job, file_content))

        return job

    async def _validate_file(self, file: UploadFile):
        """验证上传的文件"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")

        # 检查文件扩展名
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in self.supported_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型。支持的格式: {', '.join(self.supported_extensions)}"
            )

        # 检查MIME类型
        if file.content_type not in self.supported_mime_types:
            raise HTTPException(
                status_code=400,
                detail="文件类型不匹配"
            )

        # 读取并检查文件大小
        content = await file.read()
        if len(content) > self.max_file_size:
            raise HTTPException(
                status_code=400,
                detail=f"文件大小超过限制 ({self.max_file_size // (1024*1024)}MB)"
            )

        # 重置文件指针
        await file.seek(0)

    async def _process_job(self, job: Paper2CodeJob, file_content: bytes):
        """处理Paper2Code任务的主要流程"""
        try:
            await self._update_job_status(job, Paper2CodeStatus.PARSING, "解析文档中...")

            # 1. PDF/DOCX 解析
            json_file_path = os.path.join(job.output_directory, "paper_content.json")

            if job.file_type == 'pdf':
                logger.info("检测到文件类型为PDF，开始转换为JSON")
                await paper2json(
                    filename=job.original_file_name,
                    file_content=file_content,
                    file_content_type=job.mime_type,
                    json_file_path=json_file_path
                )
            elif job.file_type == 'docx':
                logger.info("检测到文件类型为DOCX，开始转换为JSON")
                # TODO: 实现DOCX解析
                await self._process_docx(file_content, json_file_path)

            job.json_file_path = json_file_path
            await job.save()
            await self._update_job_status(job, Paper2CodeStatus.PARSED, "文档解析完成", 20)

            # 2. 规划阶段
            logger.info("Paper2Code 开始规划...")
            await self._update_job_status(job, Paper2CodeStatus.PLANNING, "制定实现计划中...", 25)
            await run_planning(json_file_path, job.output_directory)
            await run_extract_config(output_dir=job.output_directory)
            await self._update_job_status(job, Paper2CodeStatus.PLANNED, "计划制定完成", 40)

            # 3. 分析阶段
            logger.info("Paper2Code 开始分析...")
            await self._update_job_status(job, Paper2CodeStatus.ANALYZING, "分析代码逻辑中...", 45)
            await run_analyzing(json_file_path, job.output_directory)
            await self._update_job_status(job, Paper2CodeStatus.ANALYZED, "逻辑分析完成", 70)

            # 4. 编码阶段
            logger.info("Paper2Code 开始编码...")
            await self._update_job_status(job, Paper2CodeStatus.CODING, "生成代码中...", 75)
            code_repo_path = os.path.join(job.output_directory, "generated_code")
            await run_coding(json_file_path, job.output_directory, code_repo_path)
            job.code_repository_path = code_repo_path
            await job.save()
            await self._update_job_status(job, Paper2CodeStatus.CODING, "代码生成完成", 90)

            # 5. 记录生成的文件
            await self._record_generated_files(job, code_repo_path)

            # 6. 打包结果
            zip_path = await self._create_result_zip(job)
            job.zip_file_path = zip_path

            # 7. 完成
            await self._update_job_status(job, Paper2CodeStatus.COMPLETED, "任务完成", 100)
            job.completed_at = datetime.now()
            job.processing_time_seconds = int((job.completed_at - job.started_at).total_seconds())
            logger.info(f"Paper2Code-{job.id} 任务完成")
            await job.save()

        except Exception as e:
            logger.error(f"处理任务失败 {job.id}: {str(e)}")
            await self._update_job_status(job, Paper2CodeStatus.FAILED, f"处理失败: {str(e)}")
            job.error_message = str(e)
            job.error_details = {"error_type": type(e).__name__, "traceback": str(e)}
            await job.save()

    async def _process_docx(self, file_content: bytes, json_file_path: str):
        """处理DOCX文件（待实现）"""
        # TODO: 实现DOCX到JSON的转换
        # 可以使用python-docx库来解析DOCX文件
        import json

        # 临时实现：创建一个空的JSON结构
        dummy_content = {
            "title": "DOCX文档",
            "content": "DOCX文档解析功能待实现",
            "sections": []
        }

        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(dummy_content, f, ensure_ascii=False, indent=2)

    async def _update_job_status(
        self,
        job: Paper2CodeJob,
        status: Paper2CodeStatus,
        message: str,
        progress: Optional[int] = None
    ):
        """更新任务状态"""
        job.status = status
        job.current_stage = message[:50]

        if progress is not None:
            job.progress_percentage = progress

        if status == Paper2CodeStatus.PARSING and not job.started_at:
            job.started_at = datetime.now()

        await job.save()
        await self._log_job_event(job, status.value.upper(), "INFO", message)

    async def _log_job_event(
        self,
        job: Paper2CodeJob,
        stage: str,
        level: str,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """记录任务处理日志"""
        await Paper2CodeLog.create(
            job=job,
            stage=stage,
            level=level,
            message=message,
            details=details or {}
        )

    async def _record_generated_files(self, job: Paper2CodeJob, code_repo_path: str):
        """记录生成的文件"""
        if not os.path.exists(code_repo_path):
            return

        file_count = 0
        for root, dirs, files in os.walk(code_repo_path):
            for file_name in files:
                file_path = os.path.join(root, file_name)
                relative_path = os.path.relpath(file_path, code_repo_path)

                # 获取文件信息
                file_size = os.path.getsize(file_path)
                file_extension = Path(file_name).suffix.lower()

                # 读取内容预览
                content_preview = None
                line_count = None

                try:
                    if file_extension in ['.py', '.js', '.java', '.cpp', '.c', '.h', '.txt', '.md', '.yaml', '.yml', '.json']:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            content_preview = content[:500] if content else None
                            line_count = len(content.splitlines()) if content else 0
                except Exception:
                    # 如果读取失败，跳过内容预览
                    pass

                # 创建文件记录
                await Paper2CodeFile.create(
                    job=job,
                    file_name=file_name,
                    file_path=file_path,
                    relative_path=relative_path,
                    file_size=file_size,
                    file_type=file_extension[1:] if file_extension else 'unknown',
                    content_preview=content_preview,
                    line_count=line_count,
                    generation_stage='coding'
                )

                file_count += 1

        job.total_files_generated = file_count
        await job.save()

    async def _create_result_zip(self, job: Paper2CodeJob) -> str:
        """创建结果ZIP文件"""
        zip_file_path = os.path.join(job.output_directory, f"{job.job_name}_result.zip")

        with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加生成的代码
            if job.code_repository_path and os.path.exists(job.code_repository_path):
                for root, dirs, files in os.walk(job.code_repository_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, job.code_repository_path)
                        zipf.write(file_path, f"generated_code/{arc_name}")

            # 添加处理过程文件
            artifacts_dir = os.path.join(job.output_directory, "planning_artifacts")
            if os.path.exists(artifacts_dir):
                for root, dirs, files in os.walk(artifacts_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, job.output_directory)
                        zipf.write(file_path, f"artifacts/{arc_name}")

        return zip_file_path

    async def get_job_by_id(self, job_id: UUID, current_user: User) -> Paper2CodeJob:
        """根据ID获取任务"""
        try:
            query = Paper2CodeJob.filter(id=job_id, is_deleted=False)

            # 权限过滤
            if current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
                pass  # 超级管理员可以查看所有
            elif current_user.role.identifier == InsetRole.ADMIN.value:
                if current_user.organization:
                    query = query.filter(organization_id=current_user.organization.id)
                else:
                    query = query.filter(created_by=current_user)
            else:
                query = query.filter(created_by=current_user)

            return await query.get()
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="任务不存在")

    async def get_user_jobs(
        self,
        current_user: User,
        limit: int = 20,
        offset: int = 0
    ) -> List[Paper2CodeJob]:
        """获取用户的任务列表"""
        query = Paper2CodeJob.filter(is_deleted=False)

        # 权限过滤
        if current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
            pass  # 超级管理员可以查看所有
        elif current_user.role.identifier == InsetRole.ADMIN.value:
            if current_user.organization:
                query = query.filter(organization_id=current_user.organization.id)
            else:
                query = query.filter(created_by=current_user)
        else:
            query = query.filter(created_by=current_user)

        return await query.order_by('-created_at').offset(offset).limit(limit).all()

    async def delete_job(self, job_id: UUID, current_user: User) -> bool:
        """删除任务"""
        job = await self.get_job_by_id(job_id, current_user)

        # 软删除
        job.is_deleted = True
        job.deleted_at = datetime.now()
        await job.save()

        await self._log_job_event(job, "DELETE", "INFO", "任务已删除")

        # 可以选择异步删除物理文件
        # asyncio.create_task(self._cleanup_job_files(job))

        return True

    async def get_download_info(self, job_id: UUID, current_user: User) -> Dict[str, Any]:
        """获取下载信息"""
        job = await self.get_job_by_id(job_id, current_user)

        if job.status != Paper2CodeStatus.COMPLETED:
            raise HTTPException(status_code=400, detail="任务未完成，无法下载")

        if not job.zip_file_path or not os.path.exists(job.zip_file_path):
            raise HTTPException(status_code=404, detail="结果文件不存在")

        file_size = os.path.getsize(job.zip_file_path)

        return {
            "file_path": job.zip_file_path,
            "file_name": f"{job.job_name}_result.zip",
            "file_size": file_size,
            "mime_type": "application/zip"
        }


# 单例服务实例
paper2code_service = Paper2CodeService()